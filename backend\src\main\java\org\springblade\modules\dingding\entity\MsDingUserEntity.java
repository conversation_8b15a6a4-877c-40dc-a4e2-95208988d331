package org.springblade.modules.dingding.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 钉钉用户信息表
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName("ms_ding_user")
@Schema(description = "钉钉用户信息")
public class MsDingUserEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 钉钉用户ID
     */
    @Schema(description = "钉钉用户ID")
    private String userid;

    /**
     * 用户姓名
     */
    @Schema(description = "用户姓名")
    private String name;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String mobile;

    /**
     * 职位
     */
    @Schema(description = "职位")
    private String title;

    /**
     * 部门ID列表（JSON格式存储）
     */
    @Schema(description = "部门ID列表")
    private String deptIdList;

    /**
     * 部门名称列表（JSON格式存储）
     */
    @Schema(description = "部门名称列表")
    private String deptNames;

    /**
     * 父级部门ID列表（JSON格式存储）
     */
    @Schema(description = "父级部门ID列表")
    private String parentDeptIds;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 状态（1-正常，0-删除）
     */
    @Schema(description = "状态")
    private Integer status;

}
