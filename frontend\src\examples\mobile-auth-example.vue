<template>
  <div class="mobile-auth-example">
    <h2>Mobile登录守卫测试页面</h2>
    
    <div class="status-section">
      <h3>当前登录状态</h3>
      <p>登录状态: <span :class="loginStatusClass">{{ loginStatusText }}</span></p>
      <p v-if="userInfo">用户信息: {{ userInfo.user_name || userInfo.account }}</p>
    </div>
    
    <div class="action-section">
      <h3>测试操作</h3>
      <button @click="simulateLogin" class="btn btn-success">模拟登录</button>
      <button @click="simulateLogout" class="btn btn-warning">模拟登出</button>
      <button @click="checkAuthStatus" class="btn btn-info">检查登录状态</button>
      <button @click="testMobileAccess" class="btn btn-primary">测试访问Mobile页面</button>
    </div>
    
    <div class="log-section">
      <h3>操作日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="btn btn-secondary">清空日志</button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'MobileAuthExample',
  data() {
    return {
      logs: []
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'token']),
    loginStatusText() {
      return this.checkMobileAuth() ? '已登录' : '未登录'
    },
    loginStatusClass() {
      return this.checkMobileAuth() ? 'status-success' : 'status-error'
    }
  },
  methods: {
    // 检查mobile登录状态（与路由守卫中的逻辑一致）
    checkMobileAuth() {
      try {
        const storedUserInfo = localStorage.getItem('userInfo');
        if (storedUserInfo) {
          const parsedUserInfo = JSON.parse(storedUserInfo);
          return !!(parsedUserInfo.access_token && parsedUserInfo.user_id);
        }
        return false;
      } catch (e) {
        this.addLog('检查登录状态失败: ' + e.message);
        return false;
      }
    },
    
    // 模拟登录
    simulateLogin() {
      const mockUserInfo = {
        access_token: 'mock_access_token_' + Date.now(),
        refresh_token: 'mock_refresh_token_' + Date.now(),
        user_id: 'test_user_123',
        user_name: 'Test User',
        account: 'testuser',
        role_name: 'user',
        tenant_id: '000000'
      };
      
      localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
      this.addLog('模拟登录成功: ' + mockUserInfo.user_name);
      
      // 更新Vuex状态（如果需要）
      this.$store.commit('SET_USER_INFO', mockUserInfo);
    },
    
    // 模拟登出
    simulateLogout() {
      localStorage.removeItem('userInfo');
      this.addLog('模拟登出成功');
      
      // 清除Vuex状态（如果需要）
      this.$store.commit('SET_USER_INFO', {});
    },
    
    // 检查登录状态
    checkAuthStatus() {
      const isLoggedIn = this.checkMobileAuth();
      this.addLog(`当前登录状态: ${isLoggedIn ? '已登录' : '未登录'}`);
      
      if (isLoggedIn) {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        this.addLog(`用户信息: ${userInfo.user_name || userInfo.account}`);
      }
    },
    
    // 测试访问Mobile页面
    testMobileAccess() {
      this.addLog('尝试访问Mobile页面...');
      
      // 检查登录状态
      if (this.checkMobileAuth()) {
        this.addLog('登录状态验证通过，跳转到Mobile页面');
        this.$router.push('/mobile');
      } else {
        this.addLog('未登录，将触发登录守卫跳转到钉钉登录');
        // 这里会触发路由守卫，自动跳转到登录页面
        this.$router.push('/mobile');
      }
    },
    
    // 添加日志
    addLog(message) {
      const now = new Date();
      const time = now.toLocaleTimeString();
      this.logs.unshift({
        time,
        message
      });
      
      // 限制日志数量
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog('日志已清空');
    }
  },
  
  mounted() {
    this.addLog('Mobile登录守卫测试页面已加载');
    this.checkAuthStatus();
  }
}
</script>

<style scoped>
.mobile-auth-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.status-section, .action-section, .log-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

.btn {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-success { background-color: #28a745; color: white; }
.btn-warning { background-color: #ffc107; color: black; }
.btn-info { background-color: #17a2b8; color: white; }
.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }

.btn:hover {
  opacity: 0.8;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 10px;
  background-color: #fff;
  margin-bottom: 10px;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}

h2, h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>
