import router from './router/';
import store from './store';
import { getToken } from '@/utils/auth';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
NProgress.configure({ showSpinner: false });
const lockPage = '/lock'; //锁屏页

// 检查是否为mobile相关页面
function isMobilePage(path) {
  return path.startsWith('/mobile');
}

// 检查mobile页面的登录状态
function checkMobileAuth() {
  try {
    // 检查localStorage中的用户信息
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const parsedUserInfo = JSON.parse(storedUserInfo);
      // 检查是否有access_token且用户信息完整
      return !!(parsedUserInfo.access_token && parsedUserInfo.user_id);
    }
    return false;
  } catch (e) {
    console.error('检查mobile登录状态失败:', e);
    return false;
  }
}

router.beforeEach((to, from, next) => {
  const meta = to.meta || {};
  const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
  store.commit('SET_IS_MENU', isMenu === undefined);

  // 特殊处理mobile页面的登录守卫
  if (isMobilePage(to.path)) {
    if (!checkMobileAuth()) {
      // mobile页面需要登录但用户未登录，跳转到钉钉登录
      console.log('Mobile页面需要登录，跳转到钉钉登录');
      // 保存当前路径到localStorage，登录成功后返回
      localStorage.setItem('redirectPath', to.fullPath);
      // 跳转到钉钉登录页面
      next('/dinglogin');
      return;
    }
    // mobile页面已登录，直接通过
    next();
    return;
  }

  // 原有的后台页面登录逻辑保持不变
  if (getToken()) {
    if (store.getters.isLock && to.path !== lockPage) {
      //如果系统激活锁屏，全部跳转到锁屏页
      next({ path: lockPage });
    } else if (to.path === '/login') {
      //如果登录成功访问登录页跳转到主页
      next({ path: '/' });
    } else {
      if (store.getters.token.length === 0) {
        store.dispatch('FedLogOut').then(() => {
          next({ path: '/login' });
        });
      } else {
        const meta = to.meta || {};
        const query = to.query || {};
        if (meta.target) {
          window.open(query.url.replace(/#/g, '&'));
          return;
        } else if (meta.isTab !== false) {
          store.commit('ADD_TAG', {
            name: query.name || to.name,
            path: to.path,
            fullPath: to.path,
            params: to.params,
            query: to.query,
            meta: meta,
          });
        }
        next();
      }
    }
  } else {
    //判断是否需要认证，没有登录访问去登录页
    if (meta.isAuth === false) {
      next();
    } else {
      next('/login');
    }
  }
});

router.afterEach(to => {
  NProgress.done();
  let title = router.$avueRouter.generateTitle(to, { label: 'name' });
  router.$avueRouter.setTitle(title);
  store.commit('SET_IS_SEARCH', false);
});
