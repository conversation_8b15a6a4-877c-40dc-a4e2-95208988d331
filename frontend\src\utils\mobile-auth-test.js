/**
 * Mobile页面登录守卫测试工具
 * 用于测试mobile页面的登录守卫功能
 */

// 模拟用户登录状态
export function simulateLogin() {
  const mockUserInfo = {
    access_token: 'mock_access_token_' + Date.now(),
    refresh_token: 'mock_refresh_token_' + Date.now(),
    user_id: 'test_user_123',
    user_name: 'Test User',
    account: 'testuser',
    role_name: 'user',
    tenant_id: '000000'
  };
  
  localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
  console.log('模拟登录成功:', mockUserInfo);
  return mockUserInfo;
}

// 模拟用户登出
export function simulateLogout() {
  localStorage.removeItem('userInfo');
  console.log('模拟登出成功');
}

// 检查当前登录状态
export function checkCurrentAuthStatus() {
  try {
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const parsedUserInfo = JSON.parse(storedUserInfo);
      const isLoggedIn = !!(parsedUserInfo.access_token && parsedUserInfo.user_id);
      console.log('当前登录状态:', isLoggedIn ? '已登录' : '未登录');
      console.log('用户信息:', parsedUserInfo);
      return isLoggedIn;
    }
    console.log('当前登录状态: 未登录');
    return false;
  } catch (e) {
    console.error('检查登录状态失败:', e);
    return false;
  }
}

// 测试路由守卫
export function testMobileRouteGuard() {
  console.log('=== Mobile页面路由守卫测试 ===');
  
  console.log('1. 测试未登录状态访问mobile页面');
  simulateLogout();
  checkCurrentAuthStatus();
  console.log('请尝试访问 /mobile 页面，应该会跳转到IDAAS登录');
  
  setTimeout(() => {
    console.log('\n2. 测试已登录状态访问mobile页面');
    simulateLogin();
    checkCurrentAuthStatus();
    console.log('请尝试访问 /mobile 页面，应该可以正常访问');
  }, 2000);
}

// 在浏览器控制台中使用的快捷方法
if (typeof window !== 'undefined') {
  window.mobileAuthTest = {
    login: simulateLogin,
    logout: simulateLogout,
    check: checkCurrentAuthStatus,
    test: testMobileRouteGuard
  };
  
  console.log('Mobile认证测试工具已加载，可在控制台使用:');
  console.log('- mobileAuthTest.login() // 模拟登录');
  console.log('- mobileAuthTest.logout() // 模拟登出');
  console.log('- mobileAuthTest.check() // 检查登录状态');
  console.log('- mobileAuthTest.test() // 运行完整测试');
}
