package org.springblade.modules.dingding.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 12:54
 */
@Component
@ConfigurationProperties(prefix = "ding-app")
@Data
public class DingAppConfig {
    private String agentId;
    private String miniAppId;
    private String appKey;
    private String appSecret;
    private String corpId;
    private String apiToken;
    private String detectUrl;
    private String baseUrl;
}
