# Mobile页面钉钉登录守卫

## 🎯 功能概述

为mobile相关页面添加了专门的登录守卫，使用钉钉登录作为认证方式，确保只有已登录的用户才能访问mobile功能，同时不影响后台管理系统的正常使用。

## 🔧 实现方案

### 1. 路由守卫逻辑 (`frontend/src/permission.js`)

- **检测mobile页面**: 通过 `isMobilePage(path)` 函数检测是否为mobile相关页面（路径以 `/mobile` 开头）
- **登录状态检查**: 通过 `checkMobileAuth()` 函数检查localStorage中的用户信息
- **自动跳转**: 未登录用户访问mobile页面时，自动跳转到钉钉登录页面 (`/dinglogin`)
- **路径保存**: 将原始访问路径保存到localStorage，登录成功后自动返回

### 2. 钉钉登录页面 (`frontend/src/views/wel/login/dinglogin.vue`)

- **路由配置**: 添加了 `/dinglogin` 路由
- **登录成功跳转**: 登录成功后自动跳转到原来要访问的页面
- **默认跳转**: 如果没有保存的路径，默认跳转到 `/mobile` 页面

### 3. 路由配置 (`frontend/src/router/page/index.js`)

- 为mobile页面添加了 `isMobile: true` 标识
- 添加了钉钉登录页面路由 `/dinglogin`
- 保持 `isAuth: false`，因为登录检查在路由守卫中单独处理

## 🚀 使用流程

### 正常使用流程

1. 用户访问 `/mobile` 页面
2. 路由守卫自动检查登录状态
3. 如果未登录：
   - 保存当前路径到localStorage
   - 跳转到钉钉登录页面 (`/dinglogin`)
   - 用户完成钉钉登录
   - 登录成功后自动返回到原来要访问的mobile页面
4. 如果已登录：直接进入mobile页面

### 测试流程

1. 访问测试页面：`/test`
2. 使用页面上的"模拟登出"按钮清除登录状态
3. 点击"Mobile页面"链接，应该会跳转到钉钉登录页面
4. 使用"模拟登录"按钮模拟登录成功
5. 再次访问Mobile页面，应该可以直接进入

## 📋 可用的测试页面

- `/test` - 简单测试页面，包含所有测试链接
- `/mobile-auth-test` - 详细的登录守卫测试页面
- `/dev-tools` - 开发工具页面
- `/dinglogin` - 钉钉登录页面
- `/mobile` - Mobile主页面（受登录守卫保护）

## 🔍 核心代码逻辑

### 路由守卫核心逻辑

```javascript
// 特殊处理mobile页面的登录守卫
if (isMobilePage(to.path)) {
  if (!checkMobileAuth()) {
    // mobile页面需要登录但用户未登录，跳转到钉钉登录
    console.log('Mobile页面需要登录，跳转到钉钉登录');
    // 保存当前路径到localStorage，登录成功后返回
    localStorage.setItem('redirectPath', to.fullPath);
    // 跳转到钉钉登录页面
    next('/dinglogin');
    return;
  }
  // mobile页面已登录，直接通过
  next();
  return;
}
```

### 钉钉登录成功跳转逻辑

```javascript
// 登录成功后的跳转处理
const handleLoginSuccess = () => {
  // 获取保存的重定向路径
  const redirectPath = localStorage.getItem('redirectPath') || '/mobile'
  // 清除保存的路径
  localStorage.removeItem('redirectPath')
  console.log('钉钉登录成功，跳转到:', redirectPath)
  // 跳转到目标页面
  router.push(redirectPath)
}
```

### 登录状态检查逻辑

```javascript
function checkMobileAuth() {
  try {
    // 检查localStorage中的用户信息
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const parsedUserInfo = JSON.parse(storedUserInfo);
      // 检查是否有access_token且用户信息完整
      return !!(parsedUserInfo.access_token && parsedUserInfo.user_id);
    }
    return false;
  } catch (e) {
    console.error('检查mobile登录状态失败:', e);
    return false;
  }
}
```

## ✅ 功能特点

- **✅ 不影响后台功能**: 后台管理系统的登录逻辑完全保持原样
- **✅ 钉钉登录集成**: 使用钉钉登录作为mobile页面的认证方式
- **✅ 智能跳转**: 未登录用户自动跳转到钉钉登录，登录后返回原页面
- **✅ 路径保存**: 自动保存和恢复用户原始访问路径
- **✅ 统一状态管理**: mobile和后台可以共享登录状态
- **✅ 错误处理**: 包含完善的错误处理和日志记录

## 🔧 快速测试

1. **清除登录状态**:
   ```javascript
   localStorage.removeItem('userInfo');
   ```

2. **模拟登录**:
   ```javascript
   const mockUserInfo = {
     access_token: 'mock_token_' + Date.now(),
     user_id: 'test_user_123',
     user_name: 'Test User',
     account: 'testuser'
   };
   localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
   ```

3. **测试访问**:
   ```javascript
   window.location.href = '/mobile';
   ```

## 📝 注意事项

1. **钉钉环境**: 钉钉登录需要在钉钉环境中才能正常工作
2. **后台兼容**: 后台管理系统的登录逻辑完全不受影响
3. **路径保存**: 使用localStorage保存重定向路径，确保登录后能返回原页面
4. **状态同步**: mobile页面和后台系统可以共享登录状态

## 🚀 扩展说明

如果需要为更多mobile相关页面添加登录守卫，只需要：

1. 确保页面路径以 `/mobile` 开头，或者
2. 修改 `isMobilePage()` 函数的判断逻辑

例如，如果有 `/m/` 开头的页面也需要登录守卫：

```javascript
function isMobilePage(path) {
  return path.startsWith('/mobile') || path.startsWith('/m/');
}
```
