# Mobile页面登录守卫说明

## 功能概述

为mobile相关页面添加了专门的登录守卫，确保只有已登录的用户才能访问mobile功能，同时不影响后台管理系统的正常使用。

## 实现方案

### 1. 路由守卫逻辑 (`frontend/src/permission.js`)

- **检测mobile页面**: 通过 `isMobilePage(path)` 函数检测是否为mobile相关页面（路径以 `/mobile` 开头）
- **登录状态检查**: 通过 `checkMobileAuth()` 函数检查localStorage中的用户信息
- **自动跳转**: 未登录用户访问mobile页面时，自动跳转到IDAAS登录
- **后台功能不受影响**: 后台管理系统的登录逻辑保持原样

### 2. 路由配置 (`frontend/src/router/page/index.js`)

- 为mobile页面添加了 `isMobile: true` 标识
- 保持 `isAuth: false`，因为登录检查在路由守卫中单独处理

### 3. 组件优化 (`frontend/src/views/wel/mobile.vue`)

- 简化了组件内的登录检查逻辑
- 由于路由守卫已确保用户登录，组件内只需处理业务逻辑

## 使用方法

### 正常使用流程

1. 用户访问 `/mobile` 页面
2. 路由守卫自动检查登录状态
3. 如果未登录，自动跳转到IDAAS登录页面
4. 登录成功后，返回到原来要访问的mobile页面
5. 如果已登录，直接进入mobile页面

### 测试方法

1. 在浏览器中导入测试工具：
```javascript
// 在浏览器控制台中执行
import('/src/utils/mobile-auth-test.js');
```

2. 使用测试命令：
```javascript
// 模拟登录
mobileAuthTest.login();

// 模拟登出
mobileAuthTest.logout();

// 检查当前登录状态
mobileAuthTest.check();

// 运行完整测试
mobileAuthTest.test();
```

## 技术细节

### 登录状态检查逻辑

```javascript
function checkMobileAuth() {
  try {
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      const parsedUserInfo = JSON.parse(storedUserInfo);
      // 检查是否有access_token且用户信息完整
      return !!(parsedUserInfo.access_token && parsedUserInfo.user_id);
    }
    return false;
  } catch (e) {
    console.error('检查mobile登录状态失败:', e);
    return false;
  }
}
```

### 路由守卫核心逻辑

```javascript
// 特殊处理mobile页面的登录守卫
if (isMobilePage(to.path)) {
  if (!checkMobileAuth()) {
    // mobile页面需要登录但用户未登录，跳转到IDAAS登录
    store.dispatch('RedirectToIdaasLogin', to.fullPath);
    return;
  }
  // mobile页面已登录，直接通过
  next();
  return;
}
```

## 注意事项

1. **不影响后台功能**: 后台管理系统的登录逻辑完全不受影响
2. **统一登录状态**: mobile页面和后台系统可以共享登录状态
3. **自动跳转**: 支持登录后自动返回到原来要访问的页面
4. **错误处理**: 包含完善的错误处理机制

## 扩展说明

如果需要为更多mobile相关页面添加登录守卫，只需要：

1. 确保页面路径以 `/mobile` 开头，或者
2. 修改 `isMobilePage()` 函数的判断逻辑

例如，如果有 `/m/` 开头的页面也需要登录守卫：

```javascript
function isMobilePage(path) {
  return path.startsWith('/mobile') || path.startsWith('/m/');
}
```
